import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import SellerLayout from "../../components/seller/SellerLayout";
import Table from "../../components/common/Table";
import { getSellerContent, toggleContentStatus } from "../../redux/slices/contentSlice";
import { toast } from "react-toastify";
import "../../styles/SellerMySportsStrategies.css";
import { SlEye } from "react-icons/sl";
import { getImageUrl } from "../../utils/constants";

const SellerMySportsStrategies = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { sellerContent, isLoading, error } = useSelector((state) => state.content);

  // Fetch seller content on component mount
  useEffect(() => {
    dispatch(getSellerContent());
  }, [dispatch]);

  // Handle error display
  useEffect(() => {
    if (error) {
      toast.error(error.message || "Failed to load strategies");
    }
  }, [error]);

  const handleToggleStatus = async (id) => {
    try {
      await dispatch(toggleContentStatus(id)).unwrap();
      toast.success("Strategy status updated successfully");
    } catch (error) {
      toast.error(error.message || "Failed to update strategy status");
    }
  };

  const handleViewDetails = (id) => {
    navigate(`/seller/strategy-details/${id}`);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const formatPrice = (price) => {
    return typeof price === "number" ? `$${price.toFixed(2)}` : price || "$0.00";
  };

  const columns = [
    { key: "no", label: "No.", className: "no" },
    {
      key: "content",
      label: "Videos/Documents",
      render: (item) => (
        <div className="video-doc">
          <div className="video-thumbnail">
            {item.thumbnailUrl ? (
              <img src={getImageUrl(item.thumbnailUrl)} alt={item.title} />
            ) : (
              <div className="placeholder-thumb">
                {item.contentType === "Video" ? "📹" : "📄"}
              </div>
            )}
          </div>
          <span className="video-title">{item.title}</span>
        </div>
      ),
    },
    {
      key: "date",
      label: "Date",
      render: (item) => formatDate(item.createdAt)
    },
    {
      key: "price",
      label: "Price",
      render: (item) => formatPrice(item.price)
    },
    {
      key: "status",
      label: "Status",
      render: (item) => (
        <label className="switch">
          <input
            type="checkbox"
            checked={item.isActive === 1}
            onChange={() => handleToggleStatus(item._id)}
            disabled={isLoading}
          />
          <span className="slider round"></span>
        </label>
      ),
    },
    {
      key: "action",
      label: "Action",
      render: (item) => (
        <div className="action-icon-container">
          <SlEye
            className="eyeicon"
            onClick={() => handleViewDetails(item._id)}
            title="View Details"
          />
        </div>
      ),
    },
  ];

  const formatData = (strategies) => {
    return strategies.map((item, index) => ({
      ...item,
      no: index + 1,
    }));
  };

  if (isLoading) {
    return (
      <SellerLayout>
        <div className="video-status-container">
          <div className="loading-container">
            <div className="loading-spinner"></div>
            <p>Loading strategies...</p>
          </div>
        </div>
      </SellerLayout>
    );
  }

  return (
    <SellerLayout>
      <div className="video-status-container">
        {sellerContent.length === 0 ? (
          <div className="empty-state">
            <h3>No strategies found</h3>
            <p>You haven't created any strategies yet. Click "Add New Strategy" to get started.</p>
          </div>
        ) : (
          <Table
            columns={columns}
            data={formatData(sellerContent)}
            className="video-table"
          />
        )}
      </div>
    </SellerLayout>
  );
};

export default SellerMySportsStrategies;
