/* Hero Section */
.home-section .hero-section {
  background-color: #141414;
  color: var(--white);

  background-image: url("../assets/images/herobackground.svg");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  overflow: hidden;
}

.home-section .hero-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 2rem;
}

.home-section .hero-content {
  max-width: 600px;
  z-index: 2;
}

.home-section .hero-title {
  font-size: var(--heading1);
  font-weight: 700;
  line-height: 1.2;
  color: var(--white);
}

.home-section .hero-tagline {
  font-size: var(--heading5);
  font-weight: 600;
  color: #c6eb07;
  line-height: 1.4;
}

.home-section .hero-description p {
  font-size: var(--basefont);
  line-height: 1.6;
  color: var(--white);
}

.home-section .hero-background {
  display: flex;
  width: 50%;
  height: 100%;
  justify-content: center;
  align-items: center;
  max-width: 500px;
}

.home-section .hero-background img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  transform: scale(1.1);
}

/* Sports Section */

.home-section .sports-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.home-section .sports-title {
  font-size: var(--heading3);
  font-weight: 600;
  color: var(--secondary-color);
}

.home-section .sports-view-all {
  text-decoration: underline;
}

.home-section .sports-cards-wrapper {
  width: 100%;
}

.home-section .sports-cards-container {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  width: 100%;
}

/* Featured Strategic Content Section */
.home-section .featured-section {
  background-color: var(--primary-light-color);
}

.home-section .featured-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.home-section .featured-title {
  font-size: var(--heading3);
  font-weight: 600;
  color: var(--secondary-color);
}

.home-section .featured-view-all {
  text-decoration: underline;
}

.home-section .featured-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}

/* Mission Section */

.home-section .mission-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
  align-items: center;
}

.home-section .mission-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.home-section .mission-title {
  font-size: var(--heading3);
  font-weight: 600;
  color: var(--secondary-color);
}

.home-section .mission-description {
  font-size: var(--basefont);
  line-height: 1.6;
  color: var(--dark-gray);
}

.home-section .mission-image {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.home-section .mission-image img {
  max-width: 100%;
  border-radius: var(--border-radius);
}

/* Offer and Join Section */
.home-section .offer-join-section {
  background-color: var(--primary-light-color);
}

.home-section .offer-join-container {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 40px;
  justify-items: center;
  justify-content: space-between;
}

.home-section .offer-join-container .vertical-line img {
  height: 450px;
}

.home-section .offer-column,
.home-section .join-column {
  display: flex;
  flex-direction: column;
}

.home-section .offer-title,
.home-section .join-title {
  white-space: nowrap;
  font-size: var(--heading4);
  font-weight: 600;
  color: var(--secondary-color);
}

.home-section .offer-list,
.home-section .join-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.home-section .offer-item,
.home-section .join-item {
  display: flex;
  align-items: flex-start;
  font-size: var(--basefont);
  line-height: 1.5;
}

.home-section .icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  color: var(--btn-color);
  font-size: 24px;
  min-width: 24px;
}

.home-section .offer-icon,
.home-section .join-icon {
  color: var(--btn-color);
}

.home-section .offer-text,
.home-section .join-text {
  flex: 1;
}

.home-section .offer-text h3,
.home-section .join-text h3 {
  font-size: var(--basefont);
  font-weight: 600;
  margin: 0 0 5px 0;
  color: var(--secondary-color);
}

.home-section .offer-text p,
.home-section .join-text p {
  font-size: var(--smallfont);
  margin: 0;
  color: var(--dark-gray);
}

.home-section .offer-item:hover .icon-container,
.home-section .join-item:hover .icon-container,
.home-section .offer-item:hover,
.home-section .join-item:hover {
  color: var(--secondary-color);
  transition: color 0.3s ease;
}

/* CTA Section */
.home-section .cta-section {
  text-align: center;
  background-image: url("../assets/images/cta-sectionbg.svg");
}

.home-section .cta-container {
  padding: 40px 20px;
  padding: 40px 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.home-section .cta-title {
  font-size: var(--heading2);
  font-weight: 700;
  color: var(--white);
  line-height: 1.2;
}

.home-section .cta-description {
  font-size: var(--basefont);
  color: var(--white);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  font-weight: 400;
}

/* Responsive styles */

@media (max-width: 1024px) {
  .home-section .sports-cards-container {
    grid-template-columns: repeat(3, 1fr);
  }

  .home-section .featured-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .home-section .offer-join-container {
    grid-template-columns: 1fr 0.1fr 1fr;
    gap: 20px;
  }

  .home-section .offer-text h3,
  .home-section .join-text h3 {
    font-size: var(--smallfont);
  }

  .home-section .offer-text p,
  .home-section .join-text p {
    font-size: var(--extrasmallfont);
  }
}
@media (max-width: 900px) {
  .home-section .hero-background img {
    max-height: 100%;
    max-width: 275px;
  }
}
@media (max-width: 768px) {
  .home-section .hero-content {
    max-width: 100%;
    text-align: center;
    justify-items: center;
  }
  .home-section .hero-container {
    flex-direction: column;
    gap: 3rem;
  }
  .home-section .hero-title {
    font-size: var(--heading2);
  }

  .home-section .hero-tagline {
    font-size: var(--heading6);
  }

  .home-section .hero-description p {
    font-size: var(--smallfont);
  }

  .home-section .hero-background {
    width: 100%;
  }

  .home-section .sports-cards-container {
    grid-template-columns: repeat(2, 1fr);
  }

  .home-section .featured-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .home-section .mission-container {
    grid-template-columns: 1fr;
  }

  .home-section .mission-image {
    order: -1;
  }

  .home-section .offer-join-container {
    grid-template-columns: 1fr;
    gap: 20px;
    padding: 0 15px;
  }

  .home-section .offer-join-container .vertical-line {
    display: none;
  }

  .home-section .offer-column,
  .home-section .join-column {
    width: 100%;
    max-width: 100%;
  }

  .home-section .icon-container {
    min-width: 20px;
    font-size: 20px;
  }

  .home-section .offer-text h3,
  .home-section .join-text h3 {
    font-size: var(--smallfont);
  }

  .home-section .offer-text p,
  .home-section .join-text p {
    font-size: var(--extrasmallfont);
  }
}

@media (max-width: 480px) {
  .home-section .sports-cards-container {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .home-section .hero-container {
    flex-direction: column;
    gap: 3rem;
  }

  .home-section .offer-title,
  .home-section .join-title {
    font-size: var(--heading5);
    text-align: center;
  }

  .home-section .offer-item,
  .home-section .join-item {
    margin-bottom: 15px;
  }

  .home-section .icon-container {
    min-width: 18px;
    font-size: 18px;
    margin-right: 10px;
  }

  .home-section .offer-text h3,
  .home-section .join-text h3 {
    font-size: var(--extrasmallfont);
    line-height: 1.3;
  }

  .home-section .offer-text p,
  .home-section .join-text p {
    font-size: var(--extrasmallfont);
    line-height: 1.3;
  }
}

@media (max-width: 360px) {
  .home-section .offer-title,
  .home-section .join-title {
    font-size: var(--heading6);
  }

  .home-section .icon-container {
    min-width: 16px;
    font-size: 16px;
    margin-right: 8px;
  }

  .home-section .offer-item,
  .home-section .join-item {
    margin-bottom: 12px;
  }
}
