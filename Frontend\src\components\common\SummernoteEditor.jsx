// import React, { useEffect, useRef } from 'react';

// // Import Summernote and its dependencies
// import 'summernote/dist/summernote-lite.css';
// import 'bootstrap/dist/css/bootstrap.css';

// const SummernoteEditor = ({
//   value = '',
//   onChange,
//   placeholder = 'Enter text here...',
//   height = 350,
//   className = '',
//   disabled = false,
//   options = {}
// }) => {
//   const editorRef = useRef(null);
//   const summernoteRef = useRef(null);

//   useEffect(() => {
//     // Dynamically import jQuery and Summernote to avoid SSR issues
//     const initializeSummernote = async () => {
//       try {
//         // Import jQuery
//         const $ = (await import('jquery')).default;

//         // Make jQuery available globally for Summernote
//         window.jQuery = $;
//         window.$ = $;

//         // Import Summernote
//         await import('summernote/dist/summernote-lite.js');

//         // Initialize Summernote
//         if (editorRef.current && !summernoteRef.current) {
//           const defaultOptions = {
//             height: height,
//             placeholder: placeholder,
//             toolbar: [
//               ['style', ['style']],
//               ['font', ['bold', 'underline', 'clear']],
//               ['fontname', ['fontname']],
//               ['para', ['ul', 'ol', 'paragraph']],
//               ['table', ['table']],
//               ['insert', ['link', 'picture', 'video']],
//               ['view', ['fullscreen', 'codeview']]
//             ],
//             callbacks: {
//               onChange: function (contents) {
//                 if (onChange) {
//                   onChange(contents);
//                 }
//               }
//             },
//             ...options
//           };

//           $(editorRef.current).summernote(defaultOptions);
//           summernoteRef.current = $(editorRef.current);

//           // Set initial value
//           if (value) {
//             $(editorRef.current).summernote('code', value);
//           }

//           // Handle disabled state
//           if (disabled) {
//             $(editorRef.current).summernote('disable');
//           }
//         }
//       } catch (error) {
//         console.error('Failed to initialize Summernote:', error);
//       }
//     };

//     initializeSummernote();

//     // Cleanup function
//     return () => {
//       if (summernoteRef.current) {
//         try {
//           summernoteRef.current.summernote('destroy');
//           summernoteRef.current = null;
//         } catch (error) {
//           console.error('Error destroying Summernote:', error);
//         }
//       }
//     };
//   }, []);

//   // Update content when value prop changes
//   useEffect(() => {
//     if (summernoteRef.current && value !== undefined) {
//       const currentContent = summernoteRef.current.summernote('code');
//       if (currentContent !== value) {
//         summernoteRef.current.summernote('code', value);
//       }
//     }
//   }, [value]);

//   // Update disabled state
//   useEffect(() => {
//     if (summernoteRef.current) {
//       if (disabled) {
//         summernoteRef.current.summernote('disable');
//       } else {
//         summernoteRef.current.summernote('enable');
//       }
//     }
//   }, [disabled]);

//   return (
//     <div className={`summernote-wrapper ${className}`}>
//       <div ref={editorRef}></div>
//     </div>
//   );
// };

// export default SummernoteEditor;



import React, { useEffect, useRef } from "react";
import "summernote/dist/summernote-lite.css";
import "bootstrap/dist/css/bootstrap.css";

const SummernoteEditor = ({
  value = "",
  onChange,
  placeholder = "Enter text...",
  height = 350,
  className = "",
  disabled = false,
}) => {
  const editorRef = useRef(null);
  const summernoteRef = useRef(null);

  // Load & Initialize Summernote
  useEffect(() => {
    const loadAndInit = async () => {
      try {
        const $ = (await import("jquery")).default;
        window.$ = $;
        window.jQuery = $;

        await import("summernote/dist/summernote-lite.js");

        if (editorRef.current && !summernoteRef.current) {
          $(editorRef.current).summernote({
            height,
            placeholder,
            toolbar: [
              ["style", ["style"]],
              ["font", ["bold", "underline", "clear"]],
              ["fontname", ["fontname"]],
              ["para", ["ul", "ol", "paragraph"]],
              ["table", ["table"]],
              ["insert", ["link", "picture", "video"]],
              ["view", ["fullscreen", "codeview"]],
            ],
            callbacks: {
              onChange: function (contents) {
                onChange?.(contents);
              },
            },
          });

          summernoteRef.current = $(editorRef.current);

          // ✅ Set initial value
          $(editorRef.current).summernote("code", value || "");

          if (disabled) {
            $(editorRef.current).summernote("disable");
          }
        }
      } catch (err) {
        console.error("Failed to load Summernote:", err);
      }
    };

    loadAndInit();

    return () => {
      if (summernoteRef.current) {
        summernoteRef.current.summernote("destroy");
        summernoteRef.current = null;
      }
    };
  }, []);

  // ✅ Update editor when `value` changes (Edit mode async data)
  useEffect(() => {
    if (summernoteRef.current && value !== undefined) {
      const current = summernoteRef.current.summernote("code");
      if (current !== value) {
        summernoteRef.current.summernote("code", value);
      }
    }
  }, [value]);

  return (
    <div className={`summernote-wrapper ${className}`}>
      <div ref={editorRef}></div>
    </div>
  );
};

export default SummernoteEditor;


// import React, { useEffect, useRef, useState } from "react";
// import "summernote/dist/summernote-lite.css";
// import "bootstrap/dist/css/bootstrap.css";

// const SummernoteEditor = ({
//   value = "",
//   onChange,
//   placeholder = "Enter text...",
//   height = 350,
//   className = "",
//   disabled = false
// }) => {
//   const editorRef = useRef(null);
//   const summernoteRef = useRef(null);
//   const [isInitialized, setIsInitialized] = useState(false);

//   useEffect(() => {
//     const init = async () => {
//       const $ = (await import("jquery")).default;
//       window.$ = $;
//       window.jQuery = $;
//       await import("summernote/dist/summernote-lite.js");

//       if (editorRef.current && !isInitialized) {
//         $(editorRef.current).summernote({
//           height,
//           placeholder,
//           toolbar: [
//             ["style", ["style"]],
//             ["font", ["bold", "underline", "clear"]],
//             ["fontname", ["fontname"]],
//             ["para", ["ul", "ol", "paragraph"]],
//             ["table", ["table"]],
//             ["insert", ["link", "picture", "video"]],
//             ["view", ["fullscreen", "codeview"]],
//           ],
//           callbacks: {
//             onChange: function (contents) {
//               onChange?.(contents);
//             },
//           },
//         });

//         summernoteRef.current = $(editorRef.current);
//         summernoteRef.current.summernote("code", value); // ✅ SET VALUE
//         if (disabled) summernoteRef.current.summernote("disable");
//         setIsInitialized(true);
//       }
//     };

//     if (value && !isInitialized) {
//       init();
//     }
//   }, [value, isInitialized]);

//   return (
//     <div className={`summernote-wrapper ${className}`}>
//       <div ref={editorRef}></div>
//     </div>
//   );
// };

// export default SummernoteEditor;
