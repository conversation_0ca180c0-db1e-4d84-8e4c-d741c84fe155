const ErrorResponse = require("../utils/errorResponse");
const Content = require("../models/Content");
const User = require("../models/User");
const { validationResult } = require("express-validator");

// @desc    Get all content
// @route   GET /api/content
// @access  Public
exports.getAllContent = async (req, res, next) => {
  try {
    let query;

    // Copy req.query
    const reqQuery = { ...req.query };

    // Fields to exclude
    const removeFields = ["page", "sport", "priceMin", "priceMax", "sortBy", "search"];

    // Loop over removeFields and delete them from reqQuery
    removeFields.forEach((param) => delete reqQuery[param]);

    // Create query string
    let queryStr = JSON.stringify(reqQuery);

    // Create operators ($gt, $gte, etc)
    queryStr = queryStr.replace(
      /\b(gt|gte|lt|lte|in)\b/g,
      (match) => `$${match}`
    );

    // Parse the query string
    let queryObj = JSON.parse(queryStr);

    // Base query - show both Published and Draft content
    const baseQuery = {
      status: "Published",
      visibility: "Public",
      isActive: { $in: [1, true] },
      ...queryObj,
    };

    // Handle sport filter
    if (req.query.sport) {
      baseQuery.sport = req.query.sport;
    }

    // Handle price range
    if (req.query.priceMin || req.query.priceMax) {
      baseQuery.price = {};
      if (req.query.priceMin) {
        baseQuery.price.$gte = parseFloat(req.query.priceMin);
      }
      if (req.query.priceMax) {
        baseQuery.price.$lte = parseFloat(req.query.priceMax);
      }
    }

    // Handle search
    if (req.query.search) {
      const searchRegex = new RegExp(req.query.search, "i");
      baseQuery.$or = [
        { title: searchRegex },
        { description: searchRegex },
        { tags: searchRegex },
      ];
    }

    // Finding resource
    query = Content.find(baseQuery);

    // Sort
    if (req.query.sortBy) {
      const sortMap = {
        'price_desc': '-price',
        'price_asc': 'price',
        'newest': '-createdAt',
        'oldest': 'createdAt',
        'rating': '-averageRating'
      };
      const sortField = sortMap[req.query.sortBy] || '-createdAt';
      query = query.sort(sortField);
    } else {
      query = query.sort("-createdAt");
    }

    // Count total before pagination
    const total = await Content.countDocuments(baseQuery);

    // Pagination
    const page = parseInt(req.query.page, 10) || 1;
    const limit = 10; // Fixed limit of 10 items per page
    const startIndex = (page - 1) * limit;
    const endIndex = page * limit;

    query = query.skip(startIndex).limit(limit);

    // Populate
    query = query.populate({
      path: "seller",
      select: "firstName lastName profileImage isVerified",
    });

    // Executing query
    const content = await query;

    // Pagination result
    const pagination = {};

    if (endIndex < total) {
      pagination.next = {
        page: page + 1,
        limit,
      };
    }

    if (startIndex > 0) {
      pagination.prev = {
        page: page - 1,
        limit,
      };
    }

    res.status(200).json({
      success: true,
      count: content.length,
      total,
      pagination,
      data: content,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get single content
// @route   GET /api/content/:id
// @access  Public
exports.getContent = async (req, res, next) => {
  try {
    const content = await Content.findById(req.params.id).populate({
      path: "seller",
      select: "firstName lastName profileImage isVerified",
    });

    if (!content) {
      return next(
        new ErrorResponse(`Content not found with id of ${req.params.id}`, 404)
      );
    }

    // Check if content is published or user is the seller or admin
    if (
      content.status !== "Published" &&
      (!req.user ||
        (req.user.id !== content.seller._id.toString() &&
          req.user.role !== "admin" &&
          req.user.role !== "buyer"))
    ) {
      return next(
        new ErrorResponse(`Content not found with id of ${req.params.id}`, 404)
      );
    }

    res.status(200).json({
      success: true,
      data: content,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Create new content
// @route   POST /api/content
// @access  Private/Seller
exports.createContent = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, errors: errors.array() });
    }

    // Add user to req.body
    req.body.seller = req.user.id;

    // Check if user is a seller
    const user = await User.findById(req.user.id);
    if (user.role !== "seller") {
      return next(
        new ErrorResponse(
          `User ${req.user.id} is not authorized to create content`,
          403
        )
      );
    }

    const content = await Content.create(req.body);

    res.status(201).json({
      success: true,
      data: content,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Update content
// @route   PUT /api/content/:id
// @access  Private/Seller
exports.updateContent = async (req, res, next) => {
  try {
    let content = await Content.findById(req.params.id);

    if (!content) {
      return next(
        new ErrorResponse(`Content not found with id of ${req.params.id}`, 404)
      );
    }

    // Make sure user is content seller
    if (
      content.seller.toString() !== req.user.id &&
      req.user.role !== "admin"
    ) {
      return next(
        new ErrorResponse(
          `User ${req.user.id} is not authorized to update this content`,
          403
        )
      );
    }

    content = await Content.findByIdAndUpdate(req.params.id, req.body, {
      new: true,
      runValidators: true,
    });

    res.status(200).json({
      success: true,
      data: content,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Delete content
// @route   DELETE /api/content/:id
// @access  Private/Seller
exports.deleteContent = async (req, res, next) => {
  try {
    const content = await Content.findById(req.params.id);

    if (!content) {
      return next(
        new ErrorResponse(`Content not found with id of ${req.params.id}`, 404)
      );
    }

    // Authorization check
    if (
      content.seller.toString() !== req.user.id &&
      req.user.role !== "admin"
    ) {
      return next(
        new ErrorResponse(
          `User ${req.user.id} is not authorized to delete this content`,
          403
        )
      );
    }

    // Soft delete by marking isActive = -1
    content.isActive = -1;
    await content.save();

    res.status(200).json({
      success: true,
      data: {},
      message: "Content soft-deleted successfully",
    });
  } catch (err) {
    next(err);
  }
};



// @desc    Get seller content
// @route   GET /api/content/seller/me
// @access  Private/Seller
exports.getSellerContent = async (req, res, next) => {
  try {
    const content = await Content.find({
      seller: req.user.id,
      isActive: 1
    });

    res.status(200).json({
      success: true,
      count: content.length,
      data: content,
    });
  } catch (err) {
    next(err);
  }
};


// @desc    Get single seller content (seller can view their own content regardless of status)
// @route   GET /api/content/seller/:id
// @access  Private/Seller
exports.getSellerContentById = async (req, res, next) => {
  try {
    const content = await Content.findById(req.params.id).populate({
      path: "seller",
      select: "firstName lastName profileImage isVerified",
    });

    if (!content) {
      return next(
        new ErrorResponse(`Content not found with id of ${req.params.id}`, 404)
      );
    }

    // Make sure user is content seller or admin
    if (
      content.seller._id.toString() !== req.user.id &&
      req.user.role !== "admin"
    ) {
      return next(
        new ErrorResponse(
          `User ${req.user.id} is not authorized to view this content`,
          403
        )
      );
    }

    res.status(200).json({
      success: true,
      data: content,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get content categories
// @route   GET /api/content/categories
// @access  Public
exports.getContentCategories = async (req, res, next) => {
  try {
    // Get unique sport types
    const sports = await Content.distinct("sport", {
      status: "Published",
      visibility: "Public",
    });

    // Get unique content types
    const contentTypes = await Content.distinct("contentType", {
      status: "Published",
      visibility: "Public",
    });

    // Get unique difficulty levels
    const difficultyLevels = await Content.distinct("difficulty", {
      status: "Published",
      visibility: "Public",
    });

    // Get price ranges
    const priceStats = await Content.aggregate([
      {
        $match: {
          status: "Published",
          visibility: "Public",
          price: { $exists: true, $ne: null },
        },
      },
      {
        $group: {
          _id: null,
          minPrice: { $min: "$price" },
          maxPrice: { $max: "$price" },
          avgPrice: { $avg: "$price" },
        },
      },
    ]);

    // Get popular tags
    const tagCounts = await Content.aggregate([
      {
        $match: {
          status: "Published",
          visibility: "Public",
          tags: { $exists: true, $ne: [] },
        },
      },
      { $unwind: "$tags" },
      {
        $group: {
          _id: "$tags",
          count: { $sum: 1 },
        },
      },
      { $sort: { count: -1 } },
      { $limit: 20 },
    ]);

    const popularTags = tagCounts.map((tag) => tag._id);

    res.status(200).json({
      success: true,
      data: {
        sports,
        contentTypes,
        difficultyLevels,
        priceRange: priceStats[0] || { minPrice: 0, maxPrice: 0, avgPrice: 0 },
        popularTags,
      },
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Toggle content active status
// @route   PUT /api/content/:id/toggle-status
// @access  Private/Seller
exports.toggleContentStatus = async (req, res, next) => {
  try {
    let content = await Content.findById(req.params.id);

    if (!content) {
      return next(
        new ErrorResponse(`Content not found with id of ${req.params.id}`, 404)
      );
    }

    // Make sure user is content seller
    if (
      content.seller.toString() !== req.user.id &&
      req.user.role !== "admin"
    ) {
      return next(
        new ErrorResponse(
          `User ${req.user.id} is not authorized to update this content`,
          403
        )
      );
    }

    // Toggle the isActive status
    const newStatus = content.isActive === 1 ? 0 : 1;

    content = await Content.findByIdAndUpdate(
      req.params.id,
      { isActive: newStatus },
      {
        new: true,
        runValidators: true,
      }
    );

    res.status(200).json({
      success: true,
      data: content,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get trending content
// @route   GET /api/content/trending
// @access  Public
exports.getTrendingContent = async (req, res, next) => {
  try {
    // Get content with highest ratings
    const topRated = await Content.find({
      status: "Published",
      visibility: "Public",
      isActive: 1,
      averageRating: { $exists: true, $gte: 4 },
    })
      .sort("-averageRating")
      .limit(5)
      .populate({
        path: "seller",
        select: "firstName lastName profileImage isVerified",
      });

    // Get most recently published content
    const newest = await Content.find({
      status: "Published",
      visibility: "Public",
      isActive: 1,
    })
      .sort("-createdAt")
      .limit(5)
      .populate({
        path: "seller",
        select: "firstName lastName profileImage isVerified",
      });

    // Get most purchased content (would require aggregation with orders)
    // This is a placeholder - you would need to implement the actual query
    const popular = await Content.find({
      status: "Published",
      visibility: "Public",
      isActive: 1,
    })
      .sort("-createdAt")
      .limit(5)
      .populate({
        path: "seller",
        select: "firstName lastName profileImage isVerified",
      });

    res.status(200).json({
      success: true,
      data: {
        topRated,
        newest,
        popular,
      },
    });
  } catch (err) {
    next(err);
  }
};
