import React, { useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import {
  selectMyDownloads,
  selectLoading,
  selectErrors,
  fetchBuyerDownloads,
  clearError
} from "../../redux/slices/buyerDashboardSlice";
import SectionWrapper from "../../components/common/SectionWrapper";
import LoadingSkeleton, { TableRowSkeleton } from "../../components/common/LoadingSkeleton";
import { ErrorDisplay } from "../../components/common/ErrorBoundary";
import { FaDownload, FaEye, FaSync } from "react-icons/fa";
import Table from "../../components/common/Table";
import "../../styles/BuyerDownloads.css";

const BuyerDownloads = () => {
  const dispatch = useDispatch();
  const downloads = useSelector(selectMyDownloads);
  const navigate = useNavigate();
  const loading = useSelector(selectLoading);
  const errors = useSelector(selectErrors);

  // Fetch downloads on component mount
  useEffect(() => {
    dispatch(fetchBuyerDownloads());
  }, [dispatch]);

  // Handle retry
  const handleRetry = () => {
    dispatch(clearError('downloads'));
    dispatch(fetchBuyerDownloads());
  };

  const columns = [
    {
      key: "no",
      label: "No.",
      className: "no",
    },
    {
      key: "orderId",
      label: "Order Id",
      className: "order-id",
    },
    {
      key: "video",
      label: "Videos/Documents",
      className: "video",
    },
    {
      key: "date",
      label: "Date",
      className: "date",
    },
    {
      key: "amount",
      label: "Amount",
      className: "amount",
    },
    {
      key: "status",
      label: "Status",
      className: "status",
    },
    {
      key: "action",
      label: "Action",
      className: "action",
    },
  ];

  const renderRow = (download, index) => (
    <>
      <div className="table-cell no">{index + 1}</div>
      <div className="table-cell order-id">#{download.id}245578</div>
      <div className="table-cell video">
        <div className="content-item">
          <div className="content-image">
            <img
              src="https://images.unsplash.com/photo-1566577739112-5180d4bf9390?q=80&w=300&h=200&auto=format&fit=crop"
              alt={download.title}
            />
          </div>
          <div className="content-info">
            <div className="content-title">{download.title}</div>
            <div className="content-coach">By {download.coach}</div>
          </div>
        </div>
      </div>
      <div className="table-cell date">{download.downloadDate} | 4:30PM</div>
      <div className="table-cell amount">$22.00</div>
      <div className="table-cell status">
        <span className="status-badge downloaded">Downloaded</span>
      </div>
      <div className="table-cell action">
        <button
          className="action-btn"
          onClick={() => navigate(`/buyer/download-details/${download.id}`)}
        >
          <FaEye />
        </button>
      </div>
    </>
  );

  return (
    <div className="BuyerDownloads">
      <SectionWrapper
        icon={<FaDownload className="BuyerSidebar__icon" />}
        title="My Downloads"
        action={
          errors.downloads && (
            <button
              className="retry-btn"
              onClick={handleRetry}
              title="Retry loading downloads"
            >
              <FaSync />
            </button>
          )
        }
      >
        {errors.downloads ? (
          <ErrorDisplay
            error={errors.downloads}
            onRetry={handleRetry}
            title="Failed to load downloads"
          />
        ) : loading.downloads ? (
          <div className="loading-container">
            <TableRowSkeleton columns={7} />
            <TableRowSkeleton columns={7} />
            <TableRowSkeleton columns={7} />
          </div>
        ) : downloads.length > 0 ? (
          <Table
            columns={columns}
            data={downloads}
            renderRow={renderRow}
            variant="grid"
            gridTemplate="0.5fr 1fr 3fr 1.5fr 1fr 1fr 0.5fr"
            className="BuyerDownloads__table"
            emptyMessage="You have no downloads yet."
          />
        ) : (
          <div className="BuyerDownloads__empty">
            <h3>No downloads yet</h3>
            <p>Your purchased content will appear here once you make your first purchase.</p>
          </div>
        )}
      </SectionWrapper>
    </div>
  );
};

export default BuyerDownloads;
