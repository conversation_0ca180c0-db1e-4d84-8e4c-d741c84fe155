import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import {
  selectNotifications,
  selectUnreadNotificationsCount,
  selectLoading,
  fetchBuyerNotifications,
} from '../../redux/slices/buyerDashboardSlice';
import { markNotificationAsRead } from '../../services/dashboardService';
import { FaBell, FaTimes, FaCheck, FaEye } from 'react-icons/fa';
import '../../styles/NotificationCenter.css';

const NotificationCenter = () => {
  const dispatch = useDispatch();
  const [isOpen, setIsOpen] = useState(false);
  const notifications = useSelector(selectNotifications);
  const unreadCount = useSelector(selectUnreadNotificationsCount);
  const loading = useSelector(state => state.buyerDashboard.loading.notifications);

  useEffect(() => {
    // Fetch notifications on component mount
    dispatch(fetchBuyerNotifications());
    
    // Set up polling for new notifications every 30 seconds
    const interval = setInterval(() => {
      dispatch(fetchBuyerNotifications());
    }, 30000);

    return () => clearInterval(interval);
  }, [dispatch]);

  const handleToggle = () => {
    setIsOpen(!isOpen);
  };

  const handleMarkAsRead = async (notificationId) => {
    try {
      await markNotificationAsRead(notificationId);
      // Refresh notifications after marking as read
      dispatch(fetchBuyerNotifications());
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
    }
  };

  const handleMarkAllAsRead = async () => {
    try {
      // Mark all unread notifications as read
      const unreadNotifications = notifications.filter(n => !n.read);
      await Promise.all(
        unreadNotifications.map(n => markNotificationAsRead(n.id))
      );
      dispatch(fetchBuyerNotifications());
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error);
    }
  };

  const formatTimeAgo = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);

    if (diffInSeconds < 60) return 'Just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
    return `${Math.floor(diffInSeconds / 86400)}d ago`;
  };

  const getNotificationIcon = (type) => {
    switch (type) {
      case 'order':
        return '🛒';
      case 'bid':
        return '🔨';
      case 'request':
        return '📝';
      case 'download':
        return '⬇️';
      case 'system':
        return '⚙️';
      default:
        return '📢';
    }
  };

  return (
    <div className="notification-center">
      <button 
        className="notification-trigger"
        onClick={handleToggle}
        aria-label={`Notifications (${unreadCount} unread)`}
      >
        <FaBell />
        {unreadCount > 0 && (
          <span className="notification-badge">{unreadCount}</span>
        )}
      </button>

      {isOpen && (
        <>
          <div className="notification-overlay" onClick={handleToggle} />
          <div className="notification-dropdown">
            <div className="notification-header">
              <h3>Notifications</h3>
              <div className="notification-actions">
                {unreadCount > 0 && (
                  <button 
                    className="mark-all-read-btn"
                    onClick={handleMarkAllAsRead}
                    title="Mark all as read"
                  >
                    <FaCheck />
                  </button>
                )}
                <button 
                  className="close-btn"
                  onClick={handleToggle}
                  title="Close"
                >
                  <FaTimes />
                </button>
              </div>
            </div>

            <div className="notification-list">
              {loading ? (
                <div className="notification-loading">
                  <div className="loading-spinner"></div>
                  <p>Loading notifications...</p>
                </div>
              ) : notifications.length > 0 ? (
                notifications.map((notification) => (
                  <div 
                    key={notification.id}
                    className={`notification-item ${!notification.read ? 'unread' : ''}`}
                  >
                    <div className="notification-icon">
                      {getNotificationIcon(notification.type)}
                    </div>
                    <div className="notification-content">
                      <h4 className="notification-title">{notification.title}</h4>
                      <p className="notification-message">{notification.message}</p>
                      <span className="notification-time">
                        {formatTimeAgo(notification.createdAt)}
                      </span>
                    </div>
                    <div className="notification-actions">
                      {!notification.read && (
                        <button 
                          className="mark-read-btn"
                          onClick={() => handleMarkAsRead(notification.id)}
                          title="Mark as read"
                        >
                          <FaEye />
                        </button>
                      )}
                    </div>
                  </div>
                ))
              ) : (
                <div className="notification-empty">
                  <p>No notifications yet</p>
                </div>
              )}
            </div>

            {notifications.length > 5 && (
              <div className="notification-footer">
                <button className="view-all-btn">
                  View All Notifications
                </button>
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
};

export default NotificationCenter;
