import React, { useState, useEffect } from "react";
import "../../styles/BuyerDashboard.css";
import StrategyCard from "../../components/common/StrategyCard";
import { useSelector, useDispatch } from "react-redux";
import {
  selectAllStrategies,
  selectLoading,
  selectErrors,
  selectFiltersBySection,
  selectSearchTermBySection,
  selectPaginationBySection,
  fetchAllStrategies,
  updateFilters,
  updateSearchTerm,
  updatePagination,
  clearError
} from "../../redux/slices/buyerDashboardSlice";
import LoadingSkeleton, { StrategiesGridSkeleton } from "../../components/common/LoadingSkeleton";
import { ErrorDisplay } from "../../components/common/ErrorBoundary";
import { IoMdSearch } from "react-icons/io";
import { IoClose } from "react-icons/io5";
import { FaFilter, FaSync } from "react-icons/fa";
import { IMAGE_BASE_URL } from "../../utils/constants";
import { strategyData } from "../../data/strategyData";

// Inside the BuyerDashboard component
const BuyerDashboard = () => {
  const dispatch = useDispatch();
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);

  // Redux selectors
  const strategies = useSelector(selectAllStrategies);
  const loading = useSelector(selectLoading);
  const errors = useSelector(selectErrors);
  const filters = useSelector(state => selectFiltersBySection(state, 'strategies'));
  const searchTerm = useSelector(state => selectSearchTermBySection(state, 'strategies'));
  const pagination = useSelector(state => selectPaginationBySection(state, 'strategies'));

  // Local state for UI
  const [currentPage, setCurrentPage] = useState(pagination.page);
  const [selectedSport, setSelectedSport] = useState(filters.sport);
  const [sortOption, setSortOption] = useState(filters.sortBy);

  // Filter options
  const typeOptions = [
    { id: "Buy Now Content", label: "Buy Now Content", checked: true },
    { id: "Bid Now Content", label: "Bid Now Content", checked: false },
    { id: "All Content", label: "All Content", checked: false },


  ];

  const contenttypeOptions = [

    { id: "Video", label: "Video", checked: true },
    { id: "Document", label: "Document", checked: false },

  ];

  const difficultyLevel = [

    { id: "Beginner", label: "Beginner", checked: true },
    { id: "Intermediate", label: "Intermediate", checked: false },
    { id: "Advanced", label: "Advanced", checked: false },
    { id: "Professional", label: "Professional", checked: false },

  ];
  // Price range state
  const [priceRange, setPriceRange] = useState(filters.priceRange);

  // Filter checkboxes state
  const [checkedItems, setCheckedItems] = useState(
    filters.category.reduce((acc, cat) => ({ ...acc, [cat]: true }), {
      fundamentals: false,
      hitting: false,
      pitching: false,
      "youth-baseball": false,
      "mental-training": false,
      fielding: false,
      "strength-conditioning": false,
      drills: false,
      catching: false,
    })
  );

  // Fetch strategies on component mount and when filters change
  // Modify the useEffect to use the specific API endpoint
  useEffect(() => {
    const params = {
      page: currentPage,
      limit: pagination.limit,
      sport: selectedSport,
      category: Object.keys(checkedItems).filter(key => checkedItems[key]),
      priceMin: priceRange[0],
      priceMax: priceRange[1],
      sortBy: sortOption,
      search: searchTerm,
    };

    dispatch(fetchAllStrategies(params));
  }, [dispatch, currentPage, selectedSport, checkedItems, priceRange, sortOption, searchTerm]);


  // Add this useEffect INSIDE the component
  useEffect(() => {
    if (!strategies || strategies.length === 0) {
      console.log('Redux State:', {
        strategies,
        loading: loading.strategies,
        error: errors.strategies,
        pagination
      });
      // Map the mock data to match the expected API response format
      const mockStrategies = strategyData.map(strategy => ({
        _id: strategy.id,
        title: strategy.title,
        thumbnailUrl: strategy.image,
        price: strategy.price,
        coachName: strategy.coach,
        contentType: strategy.hasVideo ? 'Video' : 'Document',
        saleType: strategy.type === 'bid' ? 'Auction' : 'Direct'
      }));

      // Dispatch an action to set the strategies
      dispatch({
        type: 'buyerDashboard/fetchStrategies/fulfilled',
        payload: { content: mockStrategies }
      });
    }
  }, [strategies, dispatch, loading, errors, pagination]);

  // Handle retry
  const handleRetry = () => {
    dispatch(clearError('strategies'));
    const params = {
      page: currentPage,
      limit: pagination.limit,
      sport: selectedSport,
      category: Object.keys(checkedItems).filter(key => checkedItems[key]),
      priceMin: priceRange[0],
      priceMax: priceRange[1],
      sortBy: sortOption,
      search: searchTerm,
    };
    dispatch(fetchAllStrategies(params));
  };

  // Handle drawer open/close
  const toggleDrawer = () => {
    setIsDrawerOpen(!isDrawerOpen);
  };

  // Close drawer when clicking overlay
  const handleOverlayClick = (e) => {
    if (e.target.classList.contains("filter-overlay")) {
      setIsDrawerOpen(false);
    }
  };

  // Close drawer on escape key
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === "Escape") {
        setIsDrawerOpen(false);
      }
    };

    window.addEventListener("keydown", handleEscape);
    return () => window.removeEventListener("keydown", handleEscape);
  }, []);

  // Handle checkbox change
  const handleCheckboxChange = (event) => {
    setCheckedItems({
      ...checkedItems,
      [event.target.name]: event.target.checked,
    });
  };

  // Handle search input change
  const handleSearchChange = (e) => {
    const newSearchTerm = e.target.value;
    dispatch(updateSearchTerm({ section: 'strategies', searchTerm: newSearchTerm }));
  };

  // Handle sort change
  const handleSortChange = (e) => {
    const newSortOption = e.target.value;
    setSortOption(newSortOption);
    dispatch(updateFilters({
      section: 'strategies',
      filters: { ...filters, sortBy: newSortOption }
    }));
  };

  // Handle pagination
  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
    dispatch(updatePagination({
      section: 'strategies',
      pagination: { ...pagination, page: pageNumber }
    }));
  };

  // Clear all filters
  const clearFilters = () => {
    const clearedItems = {
      fundamentals: false,
      hitting: false,
      pitching: false,
      "youth-baseball": false,
      "mental-training": false,
      fielding: false,
      "strength-conditioning": false,
      drills: false,
      catching: false,
    };
    const clearedPriceRange = [0, 1000];

    setCheckedItems(clearedItems);
    setPriceRange(clearedPriceRange);

    dispatch(updateFilters({
      section: 'strategies',
      filters: {
        ...filters,
        category: [],
        priceRange: clearedPriceRange
      }
    }));
  };

  // Generate pagination items
  const renderPaginationItems = () => {
    const totalPages = Math.ceil(pagination.total / pagination.limit) || 1;
    const items = [];

    // Previous button
    items.push(
      <button
        key="prev"
        className="pagination-arrow"
        onClick={() => currentPage > 1 && handlePageChange(currentPage - 1)}
        disabled={currentPage === 1}
      >
        &lt;
      </button>
    );

    // Page numbers
    for (let i = 1; i <= 7; i++) {
      if (
        i === 1 ||
        i === totalPages ||
        (i >= currentPage - 1 && i <= currentPage + 1)
      ) {
        items.push(
          <button
            key={i}
            className={`pagination-item ${currentPage === i ? "active" : ""}`}
            onClick={() => handlePageChange(i)}
          >
            {i}
          </button>
        );
      } else if (i === currentPage - 2 || i === currentPage + 2) {
        items.push(
          <span key={`ellipsis-${i}`} className="pagination-ellipsis">
            ...
          </span>
        );
      }
    }

    // Next button
    items.push(
      <button
        key="next"
        className="pagination-arrow"
        onClick={() =>
          currentPage < totalPages && handlePageChange(currentPage + 1)
        }
        disabled={currentPage === totalPages}
      >
        &gt;
      </button>
    );

    return items;
  };

  return (
    <div className="buyer-dashboard">
      <div className=" max-container">
        {/* Filter Overlay */}
        {isDrawerOpen && (
          <div className="filter-overlay" onClick={handleOverlayClick} />
        )}

        {/* Filter Section */}
        <div className={`filter-section ${isDrawerOpen ? "drawer-open" : ""}`}>
          <button className="close-drawer-btn" onClick={toggleDrawer}>
            <IoClose />
          </button>
          <div className="filter-header">
            <h3>Filter By</h3>
            <button className="clear-all" onClick={clearFilters}>
              Clear All
            </button>
          </div>

          {/* Sport Filter */}
          <div className="filter-group">
            <h4>Sport</h4>
            <div className="sport-select-wrapper">
              <select
                className="sport-select"
                value={selectedSport}
                onChange={(e) => setSelectedSport(e.target.value)}
              >
                <option value="All">All</option>
                <option value="Baseball">Baseball</option>
                <option value="Basketball">Basketball</option>
                <option value="Football">Football</option>
                <option value="Soccer">Soccer</option>
              </select>
            </div>
          </div>

          {/* Types Filters */}
          <div className="filter-group">
            <h4>Type</h4>
            <div className="checkbox-group">
              {typeOptions.map((option) => (
                <div className="checkbox-item" key={option.id}>
                  <input
                    type="checkbox"
                    id={option.id}
                    name={option.id}
                    checked={checkedItems[option.id]}
                    onChange={handleCheckboxChange}
                  />
                  <label htmlFor={option.id}>{option.label}</label>
                </div>
              ))}
            </div>
          </div>
          {/* Content Types Filters */}
          <div className="filter-group">
            <h4>Content Type

            </h4>
            <div className="checkbox-group">
              {contenttypeOptions.map((option) => (
                <div className="checkbox-item" key={option.id}>
                  <input
                    type="checkbox"
                    id={option.id}
                    name={option.id}
                    checked={checkedItems[option.id]}
                    onChange={handleCheckboxChange}
                  />
                  <label htmlFor={option.id}>{option.label}</label>
                </div>
              ))}
            </div>
          </div>
          {/* Dificulty Filters */}
          <div className="filter-group">
            <h4>Difficulty Level

            </h4>
            <div className="checkbox-group">
              {difficultyLevel.map((option) => (
                <div className="checkbox-item" key={option.id}>
                  <input
                    type="checkbox"
                    id={option.id}
                    name={option.id}
                    checked={checkedItems[option.id]}
                    onChange={handleCheckboxChange}
                  />
                  <label htmlFor={option.id}>{option.label}</label>
                </div>
              ))}
            </div>
          </div>

          {/* Price Filter */}
          <div className="filter-group">
            <h4>Price</h4>
            <div className="price-range">
              <div
                className="price-slider-container"
                style={{
                  "--min-value": priceRange[0],
                  "--max-value": priceRange[1],
                }}
              >
                <input
                  type="range"
                  min="0"
                  max="1000"
                  step="10"
                  value={priceRange[0]}
                  onChange={(e) => {
                    const newMin = parseInt(e.target.value);
                    if (newMin < priceRange[1] - 10) {
                      setPriceRange([newMin, priceRange[1]]);
                    }
                  }}
                  className="price-slider min-slider"
                />
                <input
                  type="range"
                  min="0"
                  max="1000"
                  step="10"
                  value={priceRange[1]}
                  onChange={(e) => {
                    const newMax = parseInt(e.target.value);
                    if (newMax > priceRange[0] + 10) {
                      setPriceRange([priceRange[0], newMax]);
                    }
                  }}
                  className="price-slider max-slider"
                />
              </div>
              <div className="price-labels">
                <span>${priceRange[0].toLocaleString()}</span>
                <span>${priceRange[1].toLocaleString()}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Content Section */}
        <div className="content-section">
          <div className="content-header">
            <div className="content-title-section">
              <h2 className="content-title">
                Featured Strategic Content
                <span>({pagination.total || strategies.length} Contents Found)</span>
              </h2>
              {errors.strategies && (
                <button
                  className="retry-btn"
                  onClick={handleRetry}
                  title="Retry loading content"
                >
                  <FaSync />
                </button>
              )}
            </div>

            <div className="search-sort">
              <div className="search-container">
                <input
                  type="text"
                  placeholder="Search..."
                  value={searchTerm}
                  onChange={handleSearchChange}
                  className="search-input"
                />
                <button className="search-button">
                  <IoMdSearch />
                </button>
              </div>

              <div className="sort-container">
                <select
                  id="sort"
                  className="sort-select"
                  value={sortOption}
                  onChange={handleSortChange}
                >
                  <option value="price_desc">Price high to low</option>
                  <option value="price_asc">Price low to high</option>
                  <option value="created_desc">Newest first</option>
                  <option value="created_asc">Oldest first</option>
                  <option value="popular">Most popular</option>
                </select>
              </div>
            </div>
          </div>
          <button className="filter-toggle-btn" onClick={toggleDrawer}>
            Filter <FaFilter />
          </button>

          {/* Error Display */}
          {errors.strategies ? (
            <ErrorDisplay
              error={errors.strategies}
              onRetry={handleRetry}
              title="Failed to load content"
              className="strategies-error"
            />
          ) : loading.strategies ? (
            /* Loading Skeleton */
            <StrategiesGridSkeleton count={pagination.limit} />
          ) : (
            <>
              {/* Strategy Cards Grid */}
              <div className="strategy-grid">
                {strategies.length > 0 ? (
                  strategies.map((strategy) => (
                    <StrategyCard
                      key={strategy._id || strategy.id}
                      id={strategy._id || strategy.id}
                      image={strategy.thumbnailUrl ? `${IMAGE_BASE_URL}${strategy.thumbnailUrl}` : 'https://via.placeholder.com/300x200?text=No+Image'}
                      title={strategy.title}
                      coach={strategy.seller?.fullName || strategy.coachName || 'Unknown Coach'}
                      price={strategy.price || 0}
                      hasVideo={strategy.contentType === 'Video'}
                      type={strategy.saleType === 'Auction' ? 'bid' : 'buy'}
                    />
                  ))
                ) : (
                  <div className="empty-state">
                    <h3>No content found</h3>
                    <p>Try adjusting your filters or search terms.</p>
                    <button className="clear-filters-btn" onClick={clearFilters}>
                      Clear All Filters
                    </button>
                  </div>
                )}
              </div>

              {/* Pagination */}
              {strategies.length > 0 && (
                <div className="pagination">{renderPaginationItems()}</div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default BuyerDashboard;


