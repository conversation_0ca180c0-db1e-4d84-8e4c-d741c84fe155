import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useParams, useNavigate } from "react-router-dom";
import SellerLayout from "../../components/seller/SellerLayout";
import { FiUpload } from "react-icons/fi";
import {
  getSellerContentById,
  updateContent,
  uploadContentFile
} from "../../redux/slices/contentSlice";
import SummernoteEditor from "../../components/common/SummernoteEditor";
import { showSuccess, showError } from "../../utils/toast";
import { getImageUrl } from "../../utils/constants";
import "../../styles/AddStrategy.css";

const EditStrategy = () => {
  const { id } = useParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { singleContent, isLoading } = useSelector((state) => state.content);

  // State for array field inputs
  const [newTag, setNewTag] = useState("");

  // Form state - Visible fields as per screenshot
  const [formData, setFormData] = useState({
    // Visible fields
    title: "",
    category: "",
    coachName: "",
    description: "",
    fileUrl: "",
    aboutCoach: "",
    strategicContent: "",

    // Hidden fields with default values for backend compatibility
    sport: "Other",
    contentType: "Video",
    previewUrl: "",
    thumbnailUrl: "",
    duration: "",
    videoLength: "",
    fileSize: "",
    tags: [],
    difficulty: "Intermediate",
    language: "English",
    prerequisites: [],
    learningObjectives: [],
    equipment: [],
    saleType: "Fixed",
    price: 0,
    allowCustomRequests: false,
    customRequestPrice: "",
    status: "Draft",
    visibility: "Public"
  });

  // File upload state
  const [uploadedFile, setUploadedFile] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Fetch content data when component mounts
  useEffect(() => {
    if (id) {
      dispatch(getSellerContentById(id));
    }
  }, [dispatch, id]);

  // Populate form when content is loaded
  useEffect(() => {
    if (singleContent) {
      setFormData({
        title: singleContent.title || "",
        category: singleContent.category || "",
        coachName: singleContent.coachName || "",
        description: singleContent.description || "",
        fileUrl: singleContent.fileUrl || "",
        aboutCoach: singleContent.aboutCoach || "",
        strategicContent: singleContent.strategicContent || "",

        // Hidden fields
        sport: singleContent.sport || "Other",
        contentType: singleContent.contentType || "Video",
        previewUrl: singleContent.previewUrl || "",
        thumbnailUrl: singleContent.thumbnailUrl || "",
        duration: singleContent.duration || "",
        videoLength: singleContent.videoLength || "",
        fileSize: singleContent.fileSize || "",
        tags: singleContent.tags || [],
        difficulty: singleContent.difficulty || "Intermediate",
        language: singleContent.language || "English",
        prerequisites: singleContent.prerequisites || [],
        learningObjectives: singleContent.learningObjectives || [],
        equipment: singleContent.equipment || [],
        saleType: singleContent.saleType || "Fixed",
        price: singleContent.price || 0,
        allowCustomRequests: singleContent.allowCustomRequests || false,
        customRequestPrice: singleContent.customRequestPrice || "",
        status: singleContent.status || "Draft",
        visibility: singleContent.visibility || "Public"
      });
    }
  }, [singleContent]);

  // Handle input changes
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  // Handle Summernote changes
  const handleSummernoteChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle file upload
  const handleFileUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    try {
      const formDataUpload = new FormData();
      formDataUpload.append('file', file);

      const response = await dispatch(uploadContentFile(formDataUpload)).unwrap();

      setFormData(prev => ({
        ...prev,
        fileUrl: response.data.fileUrl,
        fileSize: response.data.fileSize
      }));

      setUploadedFile({
        name: response.data.fileName,
        url: response.data.fileUrl,
        type: response.data.fileType,
        size: response.data.fileSize
      });

      showSuccess('File uploaded successfully!');
    } catch (error) {
      console.error('File upload failed:', error);
      showError('Failed to upload file. Please try again.');
    }
  };

  // Handle thumbnail upload
  const handleThumbnailUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    try {
      // Validate file type
      const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
      if (!validTypes.includes(file.type)) {
        throw new Error('Please upload a valid image file (JPEG, JPG, PNG, or GIF)');
      }

      // Validate file size (max 5MB)
      const maxSize = 5 * 1024 * 1024; // 5MB
      if (file.size > maxSize) {
        throw new Error('File size must be less than 5MB');
      }

      const formDataUpload = new FormData();
      formDataUpload.append('file', file);
      formDataUpload.append('type', 'thumbnail');

      const response = await dispatch(uploadContentFile(formDataUpload)).unwrap();

      if (!response.data || !response.data.fileUrl) {
        throw new Error('Invalid response from server');
      }

      // Store the complete URL path
      const thumbnailUrl = response.data.fileUrl;
      console.log('Uploaded thumbnail URL:', thumbnailUrl); // Debug log

      // Update form data with the new thumbnail URL
      setFormData(prev => ({
        ...prev,
        thumbnailUrl: thumbnailUrl
      }));

      showSuccess('Thumbnail uploaded successfully!');
    } catch (error) {
      console.error('Thumbnail upload failed:', error);
      showError(error.message || 'Failed to upload thumbnail. Please try again.');
    }
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Validate required fields
      if (!formData.thumbnailUrl) {
        throw new Error('Please upload a thumbnail image');
      }

      // Prepare data for submission
      const submitData = {
        ...formData,
        sport: formData.category || "Other",
        coachName: formData.coachName || "Coach",
        thumbnailUrl: formData.thumbnailUrl
      };

      console.log('Submitting data:', submitData); // Debug log

      await dispatch(updateContent({ id, contentData: submitData })).unwrap();

      showSuccess('🎉 Strategy updated successfully!');
      navigate(`/seller/strategy-details/${id}`);

    } catch (error) {
      console.error('Content update failed:', error);
      let errorMessage = 'Failed to update strategy. Please try again.';

      if (error.message) {
        errorMessage = error.message;
      } else if (error.errors && error.errors.length > 0) {
        errorMessage = error.errors[0].msg || errorMessage;
      } else if (typeof error === 'string') {
        errorMessage = error;
      }

      showError(`❌ ${errorMessage}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Show loading state while fetching content
  if (isLoading && !singleContent) {
    return (
      <SellerLayout>
        <div className="AddStrategy">
          <div className="loading-container">
            <div className="loading-spinner"></div>
            <p>Loading strategy details...</p>
          </div>
        </div>
      </SellerLayout>
    );
  }

  // Show error state if content not found
  if (!singleContent && !isLoading) {
    return (
      <SellerLayout>
        <div className="AddStrategy">
          <div className="error-container">
            <h3>Strategy not found</h3>
            <p>The strategy you're trying to edit doesn't exist or has been removed.</p>
            <button
              className="btn btn-primary"
              onClick={() => navigate("/seller/my-sports-strategies")}
            >
              Back to Strategies
            </button>
          </div>
        </div>
      </SellerLayout>
    );
  }

  return (
    <SellerLayout>
      <div className="AddStrategy">
        {/* Header */}
        <div className="AddStrategy__header">
          <h2 className="AddStrategy__title">Edit Strategy</h2>
          <p className="AddStrategy__subtitle">Update your strategy details</p>
        </div>

        {/* Main Form */}
        <form className="AddStrategy__form" onSubmit={handleSubmit}>
          {/* Strategy Title */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">Strategy Title</label>
            <input
              type="text"
              name="title"
              className="AddStrategy__input"
              placeholder="Add title for Strategy"
              value={formData.title}
              onChange={handleInputChange}
              required
            />
          </div>

          {/* Category */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">Category</label>
            <select
              name="category"
              className="AddStrategy__select"
              value={formData.category}
              onChange={handleInputChange}
              required
            >
              <option value="">Select Category</option>
              <option value="Basketball">Basketball</option>
              <option value="Football">Football</option>
              <option value="Soccer">Soccer</option>
              <option value="Baseball">Baseball</option>
              <option value="Tennis">Tennis</option>
              <option value="Golf">Golf</option>
              <option value="Swimming">Swimming</option>
              <option value="Track and Field">Track and Field</option>
              <option value="Volleyball">Volleyball</option>
              <option value="Hockey">Hockey</option>
              <option value="Other">Other</option>
            </select>
          </div>

          {/* Coach/Seller/Academy Name */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">Coach/Seller/Academy Name</label>
            <input
              type="text"
              name="coachName"
              className="AddStrategy__input"
              placeholder="Enter coach, seller, or academy name"
              value={formData.coachName}
              onChange={handleInputChange}
              required
            />
          </div>

          {/* Description for Strategy - Rich Text Editor */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">Description for Strategy</label>
            <SummernoteEditor
              value={formData.description}
              onChange={(value) => handleSummernoteChange('description', value)}
              placeholder="Enter a detailed description of your strategy..."
              height={200}
              className="AddStrategy__summernote"
              contentKey={`desc-${id}`}
            />
          </div>

          {/* File Upload */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">Upload Video/Document</label>
            <div className="AddStrategy__file-upload">
              <input
                type="file"
                id="file-upload"
                className="AddStrategy__file-input"
                onChange={handleFileUpload}
                accept="video/*,audio/*,.pdf,.doc,.docx,.ppt,.pptx"
              />
              <label htmlFor="file-upload" className="AddStrategy__file-label">
                <FiUpload className="AddStrategy__upload-icon" />
                <span>Choose File</span>
              </label>
              {(uploadedFile || formData.fileUrl) && (
                <div className="AddStrategy__file-info">
                  <p className="AddStrategy__file-name">
                    {uploadedFile ? uploadedFile.name : 'Current file uploaded'}
                  </p>
                  {uploadedFile && (
                    <p className="AddStrategy__file-size">
                      {(uploadedFile.size / 1024 / 1024).toFixed(2)} MB
                    </p>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* About The Coach - Rich Text Editor */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">About The Coach</label>
            <SummernoteEditor
              value={formData.aboutCoach}
              onChange={(value) => handleSummernoteChange('aboutCoach', value)}
              placeholder="Share your background, experience, and expertise..."
              height={200}
              className="AddStrategy__summernote"
            />
          </div>

          {/* Includes Strategic Content - Rich Text Editor */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">Includes Strategic Content</label>
            <SummernoteEditor
              value={formData.strategicContent}
              onChange={(value) => handleSummernoteChange('strategicContent', value)}
              placeholder="Describe what strategic content is included..."
              height={200}
              className="AddStrategy__summernote"
            />
          </div>

          {/* Thumbnail Upload */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">Thumbnail Image</label>
            <div className="AddStrategy__upload">
              <input
                type="file"
                id="thumbnail-upload"
                className="AddStrategy__file-input"
                accept="image/*"
                onChange={handleThumbnailUpload}
                style={{ display: 'none' }}
              />
              <label htmlFor="thumbnail-upload" className="AddStrategy__upload-content">
                <FiUpload className="AddStrategy__upload-icon" />
                <p className="AddStrategy__upload-text">
                  {formData.thumbnailUrl ? "Thumbnail uploaded" : "Upload thumbnail image"}
                </p>
              </label>
              {formData.thumbnailUrl && (
                <div className="AddStrategy__thumbnail-preview">
                  <img
                    src={getImageUrl(formData.thumbnailUrl)}
                    alt="Thumbnail preview"
                    onError={(e) => {
                      console.error('Thumbnail preview failed:', e);
                      e.target.src = '/placeholder-image.png'; // Fallback image
                    }}
                  />
                </div>
              )}
            </div>
          </div>

          {/* Content Type */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">Content Type</label>
            <select
              name="contentType"
              className="AddStrategy__select"
              value={formData.contentType}
              onChange={handleInputChange}
              required
            >
              <option value="">Select Content Type</option>
              <option value="Video">Video</option>
              <option value="PDF">PDF</option>
              <option value="Audio">Audio</option>
              <option value="Image">Image</option>
              <option value="Text">Text</option>
            </select>
          </div>

          {/* Difficulty Level */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">Difficulty Level</label>
            <select
              name="difficulty"
              className="AddStrategy__select"
              value={formData.difficulty}
              onChange={handleInputChange}
              required
            >
              <option value="">Select Difficulty</option>
              <option value="Beginner">Beginner</option>
              <option value="Intermediate">Intermediate</option>
              <option value="Advanced">Advanced</option>
              <option value="Expert">Expert</option>
            </select>
          </div>

          {/* Language */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">Language</label>
            <select
              name="language"
              className="AddStrategy__select"
              value={formData.language}
              onChange={handleInputChange}
              required
            >
              <option value="">Select Language</option>
              <option value="English">English</option>
              <option value="Spanish">Spanish</option>
              <option value="French">French</option>
              <option value="German">German</option>
              <option value="Chinese">Chinese</option>
              <option value="Japanese">Japanese</option>
              <option value="Other">Other</option>
            </select>
          </div>

          {/* Tags */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">Tags</label>
            <div className="AddStrategy__array-field">
              <div className="AddStrategy__array-input">
                <input
                  type="text"
                  className="AddStrategy__input"
                  placeholder="Add a tag (e.g., basketball, technique, training)..."
                  value={newTag}
                  onChange={(e) => setNewTag(e.target.value)}
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      if (newTag.trim()) {
                        setFormData(prev => ({
                          ...prev,
                          tags: [...prev.tags, newTag.trim()]
                        }));
                        setNewTag("");
                      }
                    }
                  }}
                />
                <button
                  type="button"
                  className="AddStrategy__add-btn"
                  onClick={() => {
                    if (newTag.trim()) {
                      setFormData(prev => ({
                        ...prev,
                        tags: [...prev.tags, newTag.trim()]
                      }));
                      setNewTag("");
                    }
                  }}
                >
                  Add
                </button>
              </div>
              {formData.tags.length > 0 && (
                <div className="AddStrategy__tags">
                  {formData.tags.map((tag, index) => (
                    <div key={index} className="AddStrategy__tag">
                      {tag}
                      <button
                        type="button"
                        className="AddStrategy__tag-remove"
                        onClick={() => {
                          setFormData(prev => ({
                            ...prev,
                            tags: prev.tags.filter((_, i) => i !== index)
                          }));
                        }}
                      >
                        ×
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Sale Type */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">Sale Type</label>
            <select
              name="saleType"
              className="AddStrategy__select"
              value={formData.saleType}
              onChange={handleInputChange}
              required
            >
              <option value="">Select Sale Type</option>
              <option value="Fixed">Fixed Price</option>
              <option value="Auction">Auction</option>
              <option value="Both">Both</option>
            </select>
          </div>

          {/* Price */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">Price ($)</label>
            <input
              type="number"
              name="price"
              className="AddStrategy__input"
              placeholder="Enter price"
              value={formData.price}
              onChange={handleInputChange}
              min="0"
              step="0.01"
              required
            />
          </div>

          {/* Allow Custom Requests */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__checkbox-label">
              <input
                type="checkbox"
                name="allowCustomRequests"
                checked={formData.allowCustomRequests}
                onChange={handleInputChange}
                className="AddStrategy__checkbox"
              />
              Allow Custom Requests
            </label>
          </div>

          {/* Custom Request Price (shown only if allowCustomRequests is true) */}
          {formData.allowCustomRequests && (
            <div className="AddStrategy__field">
              <label className="AddStrategy__label">Custom Request Price ($)</label>
              <input
                type="number"
                name="customRequestPrice"
                className="AddStrategy__input"
                placeholder="Enter custom request price"
                value={formData.customRequestPrice}
                onChange={handleInputChange}
                min="0"
                step="0.01"
              />
            </div>
          )}

          {/* Action Buttons */}
          <div className="AddStrategy__actions">
            <button
              type="submit"
              className="btn btn-primary AddStrategy__submit-btn"
              disabled={isSubmitting || isLoading}
            >
              {isSubmitting ? 'Updating...' : 'Update Strategy'}
            </button>
            <button
              type="button"
              className="btn btn-outline AddStrategy__reset-btn"
              onClick={() => navigate(`/seller/strategy-details/${id}`)}
              disabled={isSubmitting || isLoading}
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    </SellerLayout>
  );
};

export default EditStrategy;
