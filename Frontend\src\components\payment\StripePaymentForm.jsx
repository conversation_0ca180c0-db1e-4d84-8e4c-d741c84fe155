import { useState, useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { createPaymentIntent, confirmPayment } from '../../redux/slices/paymentSlice';
import { toast } from 'react-toastify';
import stripePromise from '../../utils/stripe';
import '../../styles/StripePaymentForm.css';

const StripePaymentForm = ({ order, onSuccess, onError, onCancel }) => {
  const dispatch = useDispatch();
  const cardElementRef = useRef(null);
  const stripeRef = useRef(null);
  const cardElementInstanceRef = useRef(null);

  const { paymentIntent, isLoading } = useSelector((state) => state.payment);

  // Debug logging
  console.log('🎯 StripePaymentForm rendered with order:', order);
  console.log('🎯 Payment state:', { paymentIntent, isLoading });
  console.log('🎯 Payment intent exists:', !!paymentIntent);
  console.log('🎯 Payment intent client secret:', paymentIntent?.clientSecret);
  console.log('🎯 Order ID for payment intent:', order?._id);

  const [processing, setProcessing] = useState(false);
  const [cardComplete, setCardComplete] = useState(false);
  const [cardError, setCardError] = useState(null);
  const [stripeLoaded, setStripeLoaded] = useState(false);
  const [billingDetails, setBillingDetails] = useState({
    name: '',
    email: '',
  });

  // Initialize Stripe and create card element
  useEffect(() => {
    const initializeStripe = async () => {
      try {
        console.log('Initializing Stripe...');
        const stripe = await stripePromise;
        if (!stripe) {
          throw new Error('Stripe failed to load');
        }

        console.log('Stripe loaded successfully:', stripe);
        stripeRef.current = stripe;

        const elements = stripe.elements();
        const cardElement = elements.create('card', {
          style: {
            base: {
              color: '#424770',
              fontFamily: '"Helvetica Neue", Helvetica, sans-serif',
              fontSmoothing: 'antialiased',
              fontSize: '16px',
              '::placeholder': {
                color: '#aab7c4',
              },
            },
            invalid: {
              color: '#9e2146',
              iconColor: '#9e2146',
            },
          },
          hidePostalCode: true,
        });

        cardElementInstanceRef.current = cardElement;

        if (cardElementRef.current) {
          cardElement.mount(cardElementRef.current);

          cardElement.on('change', (event) => {
            setCardComplete(event.complete);
            setCardError(event.error ? event.error.message : null);
          });
        }

        setStripeLoaded(true);
      } catch (err) {
        console.error('Error initializing Stripe:', err);
        setCardError('Failed to load payment form. Please refresh the page.');
      }
    };

    initializeStripe();

    // Cleanup
    return () => {
      if (cardElementInstanceRef.current) {
        cardElementInstanceRef.current.unmount();
      }
    };
  }, []);

  // Create payment intent when component mounts
  useEffect(() => {
    console.log('🚀 Payment intent useEffect triggered');
    console.log('🚀 Order exists:', !!order);
    console.log('🚀 Order ID:', order?._id);
    console.log('🚀 Dispatch function:', typeof dispatch);

    if (order && order._id) {
      console.log('🚀 Creating payment intent for order:', order._id);
      console.log('🚀 Dispatching createPaymentIntent action...');

      dispatch(createPaymentIntent({ orderId: order._id }))
        .unwrap()
        .then((result) => {
          console.log('✅ Payment intent created successfully:', result);
        })
        .catch((error) => {
          console.error('❌ Error creating payment intent:', error);
          console.error('❌ Error details:', error);
        });
    } else {
      console.log('❌ Not creating payment intent - missing order or order ID');
      console.log('❌ Order:', order);
      console.log('❌ Order._id:', order?._id);
    }
  }, [dispatch, order]);

  const handleBillingDetailsChange = (e) => {
    setBillingDetails({
      ...billingDetails,
      [e.target.name]: e.target.value,
    });
  };

  const handleSubmit = async (event) => {
    event.preventDefault();

    if (!stripeRef.current || !cardElementInstanceRef.current || !paymentIntent) {
      setCardError('Payment system not ready. Please try again.');
      return;
    }

    if (!cardComplete) {
      setCardError('Please complete your card information');
      return;
    }

    if (!billingDetails.name.trim()) {
      toast.error('Please enter the cardholder name');
      return;
    }

    setProcessing(true);
    setCardError(null);

    try {
      // Confirm the payment with Stripe
      const { error: stripeError, paymentIntent: confirmedPaymentIntent } = await stripeRef.current.confirmCardPayment(
        paymentIntent.clientSecret,
        {
          payment_method: {
            card: cardElementInstanceRef.current,
            billing_details: {
              name: billingDetails.name,
              email: billingDetails.email,
            },
          },
        }
      );

      if (stripeError) {
        setCardError(stripeError.message);
        setProcessing(false);
        if (onError) {
          onError(stripeError);
        }
        return;
      }

      if (confirmedPaymentIntent.status === 'succeeded') {
        // Confirm payment with backend
        const confirmResult = await dispatch(confirmPayment({
          orderId: order._id,
          paymentIntentId: confirmedPaymentIntent.id,
        })).unwrap();

        toast.success('Payment successful!');
        setProcessing(false);
        
        if (onSuccess) {
          onSuccess(confirmResult);
        }
      }
    } catch (err) {
      console.error('Payment error:', err);
      setCardError(err.message || 'An error occurred while processing your payment');
      setProcessing(false);
      
      if (onError) {
        onError(err);
      }
    }
  };

  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    }
  };

  if (!order) {
    return (
      <div className="stripe-payment-form">
        <div className="payment-error">
          No order information available. Please try again.
        </div>
      </div>
    );
  }

  return (
    <div className="stripe-payment-form">
      <div className="payment-header">
        <h3>Payment Information</h3>
        <p>Complete your purchase securely with Stripe</p>
      </div>

      <form onSubmit={handleSubmit} className="payment-form">
        {/* Billing Details */}
        <div className="billing-details">
          <div className="form-group">
            <label htmlFor="cardholder-name">Cardholder Name *</label>
            <input
              type="text"
              id="cardholder-name"
              name="name"
              value={billingDetails.name}
              onChange={handleBillingDetailsChange}
              placeholder="Enter cardholder name"
              className="form-input"
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor="email">Email (optional)</label>
            <input
              type="email"
              id="email"
              name="email"
              value={billingDetails.email}
              onChange={handleBillingDetailsChange}
              placeholder="Enter email for receipt"
              className="form-input"
            />
          </div>
        </div>

        {/* Card Element */}
        <div className="form-group">
          <label>Card Information *</label>
          <div className="card-element-container" ref={cardElementRef}>
            {/* Stripe card element will be mounted here */}
          </div>
          {cardError && <div className="card-error">{cardError}</div>}
        </div>

        {/* Order Summary */}
        <div className="order-summary-payment">
          <div className="summary-row">
            <span>Subtotal:</span>
            <span>${order.amount?.toFixed(2)}</span>
          </div>
          <div className="summary-row total">
            <span>Total:</span>
            <span>${order.amount?.toFixed(2)}</span>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="payment-actions">
          <button
            type="button"
            onClick={handleCancel}
            className="btn-secondary cancel-btn"
            disabled={processing}
          >
            Cancel
          </button>
          
          <button
            type="submit"
            disabled={!stripeLoaded || processing || !cardComplete || isLoading}
            className="btn-primary pay-btn"
          >
            {processing || isLoading ? (
              <>
                <span className="spinner"></span>
                Processing...
              </>
            ) : (
              `Pay $${order.amount?.toFixed(2)}`
            )}
          </button>
        </div>
      </form>

      {/* Security Notice */}
      <div className="security-notice">
        <p>🔒 Your payment information is secure and encrypted</p>
      </div>
    </div>
  );
};

export default StripePaymentForm;
