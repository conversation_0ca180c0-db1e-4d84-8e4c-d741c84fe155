/* SellerDashboard Custom Variables */
:root {
  --stats-purple: var(--primary-light-color);
  --stats-orange: #ffe9d9;
  --stats-green: #d6f5e8;
  --dashboard-padding: var(--heading5);
  --stats-gap: var(--heading5);
  --section-gap: var(--heading4);
  --card-padding: var(--basefont);
  --stats-number-size: var(--heading4);
}

.dashboard-container {
  padding: 0;
  background-color: transparent;
  font-family: "Poppins", sans-serif;
}

.stats-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--stats-gap);
  margin-bottom: var(--section-gap);
}

.stats-card {
  border-radius: var(--border-radius);
  padding: var(--stats-gap);
  background-color: var(--white);
  box-shadow: var(--box-shadow-light);
  text-align: center;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border: 1px solid var(--light-gray);
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.detailmain{
      display: flex
;
    flex-direction: column;
    align-content: center;
    justify-content: center;
    align-items: flex-start;

}
.dashboard-container .icon-round{
border-radius: 50%;
  background-color: #BF83FF;
  padding: 10px;
  font-size: var(--heading5);
  display: flex;
  align-items: center;
  justify-content: center;
  color:var(--white);
}
.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--box-shadow);
}

.stats-card h2 {
  margin: 0 0 var(--smallfont) 0;
  font-size: var(--stats-number-size);
  color: var(--text-color);
  font-weight: 700;
}

.stats-card p {
  margin: 0;
  font-size: var(--smallfont);
  color: var(--dark-gray);
  font-weight: 500;
}

.stats-card.purple {
   background-image: url("../assets/images/buyerdashboardone.svg");
  object-fit: cover;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    background-color: #F3E8FF;
    border-radius: var(--border-radius-large);
}
.icon-round.purple{
  background-color: #BF83FF;
}

.stats-card.orange {
 background-image: url("../assets/images/buyerdashboardtwo.svg");
      object-fit: cover;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    background-color: #Fff2da;
    border-radius: var(--border-radius-large);
}
.icon-round.orange{

  background-color:#FF947A ;
 
}
.stats-card.green {
    background-image: url("../assets/images/buyerdashboardthree.svg");
  object-fit: cover;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  background-color: #dcfce7;
  border-radius: var(--border-radius-large);
}
.icon-round.green{
  background-color: #3CD856;
}
.section {
  margin-bottom: var(--section-gap);
  padding: var(--stats-gap);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  background-color: var(--white);
  box-shadow: var(--box-shadow-light);
}

.section:last-child {
  margin-bottom: 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--card-padding);
  padding-bottom: var(--smallfont);
  border-bottom: 1px solid var(--light-gray);
}

.section-header h3 {
  font-size: var(--heading6);
  color: var(--text-color);
  font-weight: 600;
  margin: 0;
}

.section-header a {
  font-size: var(--smallfont);
  color: var(--btn-color);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s ease;
}

.section-header a:hover {
  color: var(--primary-color);
}

.table-container {
  overflow-x: auto;
  border-radius: var(--border-radius);
  border: 1px solid var(--light-gray);
}

table {
  width: 100%;
  border-collapse: collapse;
  font-size: var(--smallfont);
  background-color: var(--white);
  margin: 0;
}

th,
td {
  padding: var(--smallfont) var(--extrasmallfont);
  text-align: left;
  border-bottom: 1px solid var(--light-gray);
  vertical-align: middle;
}

th {
  background-color: var(--bg-gray);
  font-weight: 600;
  color: var(--text-color);
  font-size: var(--extrasmallfont);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

tbody tr:hover {
  background-color: var(--primary-light-color);
}

tbody tr:last-child td {
  border-bottom: none;
}

.video-title {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  font-weight: 500;
}

.video-icon {
  color: var(--btn-color);
  font-size: var(--basefont);
  flex-shrink: 0;
}

/* Action Icons */
.action-icons {
  
  display: flex;
  gap: var(--smallfont);
  align-items: center;
  justify-content: center;
}

.action-icon {
  font-size: var(--heading6);
  color: black;
  cursor: pointer;
  transition: all 0.2s ease;

  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
}

.action-icon:hover {
  color: var(--primary-color);
  background-color: var(--primary-light-color);
  transform: translateY(-2px);
}

/* Toggle Switch */
.switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  background-color: var(--light-gray);
  border-radius: 24px;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  transition: all 0.3s ease;
  border: 2px solid var(--light-gray);
}

.slider::before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 2px;
  bottom: 1px;
  background-color: var(--white);
  border-radius: 50%;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

input:checked + .slider {
  background-color: var(--btn-color);
  border-color: var(--btn-color);
}

input:checked + .slider::before {
  transform: translateX(18px);
}

.slider.round {
  border-radius: 34px;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .stats-container {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: var(--basefont);
  }

  .section {
    padding: var(--basefont);
  }

  th,
  td {
    padding: var(--extrasmallfont) var(--extrasmallfont);
    font-size: var(--extrasmallfont);
  }
}

@media (max-width: 768px) {
  .stats-container {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--smallfont);
  }

  .section {
    padding: var(--smallfont);
    margin-bottom: var(--basefont);
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--extrasmallfont);
  }

  .table-container {
    font-size: var(--extrasmallfont);
  }

  .video-title {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--extrasmallfont);
  }

  .action-icons {
    gap: var(--extrasmallfont);
  }
}

@media (max-width: 480px) {
  .stats-container {
    grid-template-columns: 1fr;
    gap: var(--smallfont);
  }

  .stats-card {
    padding: var(--basefont);
  }

  .stats-card h2 {
    font-size: var(--heading5);
  }

  /* Stack table content for mobile */
  .table-container {
    overflow-x: scroll;
  }

  table {
    min-width: 600px;
  }
}
