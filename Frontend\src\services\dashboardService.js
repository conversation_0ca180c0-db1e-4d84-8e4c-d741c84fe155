import api from './api';
import { DASHBOARD_ENDPOINTS, ORDER_ENDPOINTS, BID_ENDPOINTS, REQUEST_ENDPOINTS } from '../utils/constants';

/**
 * Dashboard Service for Buyer-specific data
 */

/**
 * Get buyer dashboard stats
 * @returns {Promise} Promise with dashboard stats
 */
export const getBuyerDashboardStats = async () => {
  try {
    const response = await api.get(`${DASHBOARD_ENDPOINTS.STATS}/buyer`);
    return response.data;
  } catch (error) {
    console.error('Error fetching buyer dashboard stats:', error);
    throw error;
  }
};

/**
 * Get buyer orders with pagination and filtering
 * @param {Object} params - Query parameters
 * @returns {Promise} Promise with orders data
 */
export const getBuyerOrders = async (params = {}) => {
  try {
    const response = await api.get(ORDER_ENDPOINTS.BUYER_ORDERS, { params });
    return response.data;
  } catch (error) {
    console.error('Error fetching buyer orders:', error);
    throw error;
  }
};

/**
 * Get buyer downloads
 * @param {Object} params - Query parameters
 * @returns {Promise} Promise with downloads data
 */
export const getBuyerDownloads = async (params = {}) => {
  try {
    const response = await api.get(ORDER_ENDPOINTS.BUYER_DOWNLOADS, {
      params,
      headers: {
        Authorization: `Bearer ${localStorage.getItem('xosportshub_token')}`
      }
    });
    return Array.isArray(response.data) ? response.data : [];
  } catch (error) {
    console.error('Error fetching buyer downloads:', error);
    return [];
  }
};

/**
 * Get buyer bids
 * @param {Object} params - Query parameters
 * @returns {Promise} Promise with bids data
 */
export const getBuyerBids = async (params = {}) => {
  try {
    const response = await api.get(BID_ENDPOINTS.USER_BIDS, {
      params,
      headers: {
        Authorization: `Bearer ${localStorage.getItem('xosportshub_token')}`
      }
    });
    return Array.isArray(response.data) ? response.data : [];
  } catch (error) {
    console.error('Error fetching buyer bids:', error);
    return [];
  }
};

/**
 * Get buyer requests
 * @param {Object} params - Query parameters
 * @returns {Promise} Promise with requests data
 */
export const getBuyerRequests = async (params = {}) => {
  try {
    const response = await api.get(REQUEST_ENDPOINTS.BUYER_REQUESTS, { params });
    return response.data;
  } catch (error) {
    console.error('Error fetching buyer requests:', error);
    throw error;
  }
};

/**
 * Get buyer activity feed
 * @param {Object} params - Query parameters
 * @returns {Promise} Promise with activity data
 */
export const getBuyerActivity = async (params = {}) => {
  try {
    const response = await api.get(`${DASHBOARD_ENDPOINTS.ACTIVITY}/buyer`, { params });
    return response.data;
  } catch (error) {
    console.error('Error fetching buyer activity:', error);
    throw error;
  }
};

/**
 * Get buyer notifications
 * @returns {Promise} Promise with notifications data
 */
export const getBuyerNotifications = async () => {
  try {
    const response = await api.get('/notifications/me');
    return response.data;
  } catch (error) {
    console.error('Error fetching buyer notifications:', error);
    throw error;
  }
};

/**
 * Mark notification as read
 * @param {string} notificationId - Notification ID
 * @returns {Promise} Promise with success response
 */
export const markNotificationAsRead = async (notificationId) => {
  try {
    const response = await api.put(`/notifications/${notificationId}/read`);
    return response.data;
  } catch (error) {
    console.error('Error marking notification as read:', error);
    throw error;
  }
};

/**
 * Get buyer wishlist
 * @returns {Promise} Promise with wishlist data
 */
export const getBuyerWishlist = async () => {
  try {
    const response = await api.get('/wishlist');
    return response.data;
  } catch (error) {
    console.error('Error fetching buyer wishlist:', error);
    throw error;
  }
};

export default {
  getBuyerDashboardStats,
  getBuyerOrders,
  getBuyerDownloads,
  getBuyerBids,
  getBuyerRequests,
  getBuyerActivity,
  getBuyerNotifications,
  markNotificationAsRead,
  getBuyerWishlist,
};
